import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import matplotlib.tri as tri

def plot_simplex(ax, R, L, p, C):
    """绘制策略演化的相图"""
    # 计算收益
    E_S = p * R - (1 - p) * L
    E_V = p * R - C
    E_I = 0.0

    # 定义复制子动态方程
    def replicator_dynamics(xyz, t):
        x, y, z = xyz
        avg_payoff = x * E_S + y * E_V + z * E_I
        dx_dt = x * (E_S - avg_payoff)
        dy_dt = y * (E_V - avg_payoff)
        dz_dt = z * (E_I - avg_payoff)
        return [dx_dt, dy_dt, dz_dt]

    # 创建三角坐标系
    corners = np.array([[0, 0], [1, 0], [0.5, np.sqrt(3)*0.5]])
    triangle = tri.Triangulation(corners[:, 0], corners[:, 1])
    
    # 绘制背景和边界
    ax.triplot(triangle, 'k-', linewidth=1.0)
    ax.set_aspect('equal')
    ax.axis('off')

    # 绘制顶点标签
    ax.text(corners[0, 0] - 0.05, corners[0, 1] - 0.05, '忽略(Ignore)', ha='right')
    ax.text(corners[1, 0] + 0.05, corners[1, 1] - 0.05, '转发(Share)', ha='left')
    ax.text(corners[2, 0], corners[2, 1] + 0.05, '核查(Verify)', ha='center')

    # 绘制从不同初始点开始的演化轨迹
    initial_points = [
        [0.33, 0.33, 0.34], [0.8, 0.1, 0.1], [0.1, 0.8, 0.1],
        [0.1, 0.1, 0.8], [0.45, 0.1, 0.45], [0.1, 0.45, 0.45]
    ]
    t = np.linspace(0, 15, 100)
    
    for point in initial_points:
        sol = odeint(replicator_dynamics, point, t)
        # 将三维坐标转换为二维三角坐标
        xy_coords = np.dot(sol, corners)
        ax.plot(xy_coords[:, 0], xy_coords[:, 1], 'b', alpha=0.7)
        ax.plot(xy_coords[0, 0], xy_coords[0, 1], 'go') # 起点
        ax.plot(xy_coords[-1, 0], xy_coords[-1, 1], 'ro') # 终点 (ESS)

    ax.set_title(f'C = {C:.2f} (ESS at {np.round(sol[-1], 2)})')

# --- 主程序 ---
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义模型参数
R = 5.0; L = 10.0; p = 0.5
C_values = [1.0, 4.0, 7.0] # 低/中/高 核查成本

fig, axes = plt.subplots(1, 3, figsize=(18, 6))
fig.suptitle(f'虚假信息传播的演化稳定策略 (R={R}, L={L}, p={p})', fontsize=16)

for ax, C in zip(axes, C_values):
    plot_simplex(ax, R, L, p, C)

plt.tight_layout(rect=[0, 0, 1, 0.95])
plt.show()