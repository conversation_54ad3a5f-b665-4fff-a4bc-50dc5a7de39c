import numpy as np
import matplotlib.pyplot as plt

def plot_incentive_analysis(ax, c_h_high, c_h_low, c_l_high, c_l_low):
    """
    分析并可视化不同奖励水平下两类创作者的策略选择
    """
    # 理论上的分离均衡奖励区间
    reward_min = c_h_high - c_l_high
    reward_max = c_h_low - c_l_low
    
    rewards = np.linspace(0, 12, 100)
    
    # 计算不同奖励下，两类创作者选择高/低努力的净收益
    utility_high_h = rewards - c_h_high
    utility_high_l = -c_l_high
    utility_low_h = rewards - c_h_low
    utility_low_l = -c_l_low

    # 绘制高能力创作者的收益
    ax.plot(rewards, utility_high_h, 'b-', label='高能力者-高努力收益')
    ax.hlines(utility_high_l, 0, 12, 'b--', label=f'高能力者-低努力收益 ({utility_high_l:.1f})')
    
    # 绘制低能力创作者的收益
    ax.plot(rewards, utility_low_h, 'r-', label='低能力者-高努力收益')
    ax.hlines(utility_low_l, 0, 12, 'r--', label=f'低能力者-低努力收益 ({utility_low_l:.1f})')

    # 标记分离均衡区域
    ax.axvspan(reward_min, reward_max, color='green', alpha=0.2, label=f'分离均衡区域 ({reward_min:.1f} < R < {reward_max:.1f})')
    
    # 设置图表格式
    ax.set_title('创作者净收益 vs. 平台奖励')
    ax.set_xlabel('平台奖励 (Reward)')
    ax.set_ylabel('净收益 (Net Utility)')
    ax.legend()
    ax.grid(True)
    ax.set_ylim(-10, 5)

# --- 主程序 ---
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 1. 定义模型参数 (成本)
# 高能力者付出高/低努力的成本
C_H_HIGH = 3.0 
C_L_HIGH = 1.0
# 低能力者付出高/低努力的成本
C_H_LOW = 8.0 
C_L_LOW = 2.0

# 2. 创建图表
fig, ax = plt.subplots(figsize=(10, 7))
plot_incentive_analysis(ax, C_H_HIGH, C_H_LOW, C_L_HIGH, C_L_LOW)
plt.show()
